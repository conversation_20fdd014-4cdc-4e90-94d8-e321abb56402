import re
import time
from typing import Dict, List, Optional, Tuple, Union


class DBCParser:
    """解析DBC文件并存储其中的报文和信号信息"""

    def __init__(self):
        self.messages = {}  # 存储报文信息：message_id -> message_info
        self.signal_formulas = {}  # 存储信号的物理值计算公式：(message_id, signal_name) -> formula_info
    
    def parse(self, file_path: str) -> None:
        """
        解析DBC文件

        Args:
            file_path: DBC文件路径
        """
        with open(file_path, 'r', encoding = 'utf-8') as file:
            lines = file.readlines()

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 解析报文定义
            if line.startswith("BO_ "):
                message_id = int(line.split()[1])
                message_name = line.split()[2].rstrip(':')
                message_size = int(line.split()[3])

                # 收集报文中的信号行
                signals = []
                i += 1
                while i < len(lines) and lines[i].strip().startswith("SG_ "):
                    signals.append(lines[i].strip())
                    i += 1

                # 解析信号
                parsed_signals = {}
                for signal_line in signals:
                    signal_info = self._parse_signal(signal_line)
                    parsed_signals[signal_info["name"]] = signal_info

                # 存储报文信息
                self.messages[message_id] = {
                    "name"   : message_name,
                    "size"   : message_size,
                    "signals": parsed_signals
                }

            # 解析信号值描述（可选）
            elif line.startswith("VAL_ "):
                # 这里可以添加对信号值描述的解析
                val_pattern = re.compile(r'VAL_\s+(\d+)\s+(\w+)\s+(.*?)\s*;', re.DOTALL)
                val_entries = val_pattern.findall(line)
                
                for msg_id, sig_name, val_def in val_entries:                    
                    if int(msg_id) not in self.messages.keys():
                        continue # 不符合原来的属性

                    dsig = self.messages[int(msg_id)]['signals']
                    if sig_name not in dsig.keys():
                        continue # 不符合原来的属性
                    
                    # 解析数值与描述的映射
                    dsig[sig_name]['val_map']= {}
                    parts = re.findall(r'(\d+)\s+"([^"]*)"', val_def)
                    for num_val, text_desc in parts:
                        dsig[sig_name]['val_map'][float(num_val)] = text_desc       
                
                i += 1

            # 解析信号计算公式
            elif line.startswith("BA_DEF_ SG_ "):
                formula_info = self._parse_formula_definition(line)
                if formula_info:
                    i += 1
                    # 查找该公式定义对应的信号
                    while i < len(lines) and lines[i].strip().startswith("BA_ "):
                        signal_ref = self._parse_signal_formula(lines[i].strip(), formula_info)
                        i += 1
            else:
                i += 1

    def _parse_signal(self, line: str) -> dict:
        """解析信号定义行"""
        parts = line.split()

        signal_name = parts[1]

        setbit = parts[3].split("|")
        start_bit = int(setbit[0])
        bit_length = int(setbit[1].split("@")[0])

        # 解析字节序和类型
        isbig_order = False
        isigned = False
        if "(" in line:
            byte_order_and_type = line.split("(")[0].split()[-1]
            if "@" in byte_order_and_type:
                isbig_order = "1" not in byte_order_and_type.split("@")[1]
                isigned = "-" in byte_order_and_type.split("@")[1]


        # 解析因子和偏移量
        factor, offset = 1.0, 0.0
        if "(" in line:
            scale_offset = line.split("(")[1].split(")")[0].split(",")
            factor = float(scale_offset[0])
            offset = float(scale_offset[1])

        # 解析最小和最大值
        min_val, max_val = 0, 0
        if "[" in line:
            min_max = line.split("[")[1].split("]")[0].split("|")
            min_val = float(min_max[0])
            max_val = float(min_max[1])

        # 解析单位
        unit = ""
        if "\"" in line:
            unit = line.split("\"")[1]

        return {
            "name"      : signal_name,
            "start_bit" : start_bit,
            "bit_length": bit_length,
            "byte_order": isbig_order,
            "data_type" : isigned,
            "factor"    : factor,
            "offset"    : offset,
            "min"       : min_val,
            "max"       : max_val,
            "unit"      : unit
        }

    def _parse_formula_definition(self, line: str) -> Optional[dict]:
        """解析公式定义"""
        if "GenSigFunc" not in line:
            return None

        # 提取公式名称
        formula_name_match = re.search(r'"([^"]*)"', line)
        if not formula_name_match:
            return None

        formula_name = formula_name_match.group(1)

        # 提取参数
        param_pattern = r'$([^)]+)$'
        param_match = re.search(param_pattern, line)
        if not param_match:
            return None

        params = [p.strip() for p in param_match.group(1).split(",")]

        return {
            "name"  : formula_name,
            "params": params
        }

    def _parse_signal_formula(self, line: str, formula_info: dict) -> Tuple[int, str]:
        """解析信号的公式应用"""
        # 提取消息ID和信号名称
        pattern = r'BA_\s+"{}"\s+SG_\s+(\d+)\s+(\w+);'.format(formula_info["name"])
        match = re.search(pattern, line)

        if not match:
            return None

        message_id = int(match.group(1))
        signal_name = match.group(2)

        # 将公式与特定信号关联
        self.signal_formulas[(message_id, signal_name)] = {
            "formula_name": formula_info["name"],
            "params"      : formula_info["params"]
        }

        return (message_id, signal_name)

    def get_physical_value(self, message_id: int, signal_name: str, raw_value: Union[int, float]) -> Union[int, float]:
        """
        根据信号的物理值计算公式计算物理值

        Args:
            message_id: 报文ID
            signal_name: 信号名称
            raw_value: 原始值

        Returns:
            物理值
        """
        # 如果有自定义公式，则使用自定义公式计算
        if (message_id, signal_name) in self.signal_formulas:
            formula_info = self.signal_formulas[(message_id, signal_name)]
            params = {param: raw_value for param in formula_info["params"]}
            # 这里需要实现具体的公式解析和计算逻辑
            # 可以使用eval函数或更安全的表达式解析器
            # 示例中我们假设公式是简单的线性变换
            if len(params) == 1:
                # 如果只有一个参数，使用默认的线性公式
                return raw_value * 0.1 + 5  # 示例公式
            else:
                # 多参数情况处理
                return raw_value  # 默认返回原始值
        # 否则使用标准的因子和偏移量计算
        elif message_id in self.messages and signal_name in self.messages[message_id]["signals"]:
            signal_info = self.messages[message_id]["signals"][signal_name]
            return raw_value * signal_info["factor"] + signal_info["offset"]
        else:
            raise ValueError(f"未找到报文ID {message_id} 和信号 {signal_name} 的定义")

    def get_message_info(self, message_id: int) -> dict:
        """获取报文信息"""
        return self.messages.get(message_id, None)

    def get_all_messages(self) -> dict:
        """获取所有报文信息"""
        return self.messages

    def parse_data(self, message_id: int, data: bytes) -> dict:
        """
        解析原始CAN数据帧，返回各信号的物理值

        Args:
            message_id: 报文ID
            data: 原始CAN数据 (bytes, 长度为1~8)

        Returns:
            dict: {信号名: 物理值}
        """
        if message_id not in self.messages:
            raise ValueError(f"未找到报文ID {hex(message_id)} 的定义")

        message = self.messages[message_id]
        signals = message["signals"]
        result = {}

        allbits = ''
        for i in data:
            allbits += f"{i:08b}"

        for signal_name, signal_info in signals.items():
            start_bit = signal_info["start_bit"]
            bit_length = signal_info["bit_length"]
            isbig_order = signal_info["byte_order"]
            isigned = signal_info["data_type"]

            # 1. 计算信号覆盖的字节范围
            new_start_bit = int((7 - start_bit%8) + (start_bit//8)*8)
            new_end_bit = int(new_start_bit+bit_length)
            if new_start_bit < 0:
                print(f'{signal_name}:start bit error pos ?= {new_start_bit}')
                continue
            elif new_end_bit > len(allbits):
                print(f'{signal_name}:end bit error pos ?= {new_end_bit}')
                continue

            if isbig_order:
                cal_value = '0b' + allbits[new_start_bit: new_end_bit].replace('0b', '')
            else:
                setbits = allbits[new_start_bit: new_end_bit].split('0b')
                cal_value = '0b'
                for i in setbits[::-1]:
                    cal_value += i

            cal_value = eval(cal_value)

            # 6. 处理有符号数（补码）
            if isigned and bit_length > 0:
                sign_mask = 1 << (bit_length - 1)
                if cal_value & sign_mask:
                    cal_value -= (1 << bit_length)

            # 7. 应用公式计算物理值
            end_val = self.get_physical_value(message_id, signal_name, cal_value)
            if end_val > signal_info["max"]:
                end_val = signal_info["max"]
            elif end_val < signal_info["min"]:
                end_val = signal_info["min"]
            
            if signal_info.get('val_map', False):
                end_val = signal_info['val_map'].get(end_val, end_val)                 
            
            result[signal_name] = end_val

        return result


# 使用示例
if __name__ == "__main__":
    parser = DBCParser()
    parser.parse("output_dbc.dbc")  # 替换为实际的DBC文件路径

    # 获取物理值
    try:
        msgid = 647
        parsed_values = parser.parse_data(msgid, [0xA7, 0x1F, 0x60, 0x78, 0x00, 0x16, 0x4F, 0x16])
        for signal, value in parsed_values.items():
            print(f"解码 {signal}: {value}")


    except ValueError as e:
        print(f"错误: {e}")

