import re
from typing import  Op<PERSON>, <PERSON><PERSON>, Union


class DBCParser:
    """解析DBC文件并存储其中的报文和信号信息"""

    def __init__(self):
        self.messages = {}  # 存储报文信息：message_id -> message_info
        self.signal_formulas = {}  # 存储信号的物理值计算公式：(message_id, signal_name) -> formula_info

    def parse(self, file_path: str) -> None:
        """
        解析DBC文件

        Args:
            file_path: DBC文件路径
        """

        import chardet
        with open(file_path, "rb") as f:
            result = chardet.detect(f.read(10000))  # 检测前10KB内容

        with open(file_path, 'r', encoding = result['encoding']) as file:
            lines = file.readlines()

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 解析报文定义
            if line.startswith("BO_ "):
                message_id = int(line.split()[1])
                message_name = line.split()[2].rstrip(':')
                message_size = int(line.split()[3])

                # 收集报文中的信号行
                signals = []
                i += 1
                while i < len(lines) and lines[i].strip().startswith("SG_ "):
                    signals.append(lines[i].strip())
                    i += 1

                # 解析信号
                parsed_signals = {}
                for signal_line in signals:
                    signal_info = self._parse_signal(signal_line)
                    parsed_signals[signal_info["name"]] = signal_info

                # 存储报文信息
                self.messages[message_id] = {
                    "name"   : message_name,
                    "size"   : message_size,
                    "signals": parsed_signals
                }

            # 解析信号值描述（可选）
            # 解析信号值描述（可选）
            elif line.startswith("VAL_ "):
                # 这里可以添加对信号值描述的解析
                val_pattern = re.compile(r'VAL_\s+(\d+)\s+(\w+)\s+(.*?)\s*;', re.DOTALL)
                val_entries = val_pattern.findall(line)

                for msg_id, sig_name, val_def in val_entries:   # 取决于自定义格式
                    if int(msg_id) not in self.messages.keys():
                        continue  # 不符合原来的属性

                    dsig = self.messages[int(msg_id)]['signals']
                    if sig_name not in dsig.keys():
                        continue  # 不符合原来的属性

                    # 解析数值与描述的映射
                    dsig[sig_name]['val_map'] = {}
                    parts = re.findall(r'(\d+)\s+"([^"]*)"', val_def)
                    for num_val, text_desc in parts:
                        dsig[sig_name]['val_map'][float(num_val)] = text_desc

                i += 1

            # 解析信号计算公式
            elif line.startswith("BA_DEF_ SG_ "):
                i += 1

            # 解析信号CM_
            elif line.startswith("CM_"):
                # 解析信号注释
                sg_comments = re.findall(r'^\s*CM_\s.+?\s(.+?)\s(.+?)\s"([^"]*)"', line)
                for msg_id, sig_name, comment in sg_comments:
                    if int(msg_id) not in self.messages.keys():
                        continue  # 不符合原来的属性

                    dsig = self.messages[int(msg_id)]['signals']
                    if sig_name not in dsig.keys():
                        continue  # 不符合原来的属性

                    # 解析数值与描述的映射 长命
                    dsig[sig_name]['lname'] = comment

                i += 1

            else:
                i += 1

    def _parse_signal(self, line: str) -> dict:
        """解析信号定义行"""
        def get_val_typ(ssval: str, ityp=0):
            sval = ssval.replace(' ', '')
            if ityp == 0:
                return int(sval)
            else:
                return float(sval)

        lre = re.compile(r'^\s*SG_\s(.+?):\s(.+?)\|(.+?)@(.+?)([+-]).?\((.+?),(.+?)\).?\[(.+?)\|(.+?)\].?"([^"]*)"(.*)', re.IGNORECASE)
        fre = lre.findall(line, endpos = len(line))[0]
        if len(fre):
            return {
                "name"      : str(fre[0]).strip(),
                "start_bit" : get_val_typ(fre[1]),
                "bit_length": get_val_typ(fre[2]),
                "byte_order": '0' in fre[3],  # @0 为大端
                "data_type" : '-' in fre[4],
                "factor"    : get_val_typ(fre[5], 1),
                "offset"    : get_val_typ(fre[6], 1),
                "min"       : get_val_typ(fre[7], 1),
                "max"       : get_val_typ(fre[8], 1),
                "unit"      : fre[9]
            }
        return {}

    def _parse_formula_definition(self, line: str) -> Optional[dict]:
        """解析公式定义"""
        if "GenSigFunc" not in line:
            return None

        # 提取公式名称
        formula_name_match = re.search(r'"([^"]*)"', line)
        if not formula_name_match:
            return None

        formula_name = formula_name_match.group(1)

        # 提取参数
        param_pattern = r'$([^)]+)$'
        param_match = re.search(param_pattern, line)
        if not param_match:
            return None

        params = [p.strip() for p in param_match.group(1).split(",")]

        return {
            "name"  : formula_name,
            "params": params
        }

    def _parse_signal_formula(self, line: str, formula_info: dict) -> Tuple[int, str]:
        """解析信号的公式应用"""
        # 提取消息ID和信号名称
        pattern = r'BA_\s+"{}"\s+SG_\s+(\d+)\s+(\w+);'.format(formula_info["name"])
        match = re.search(pattern, line)

        if not match:
            return None

        message_id = int(match.group(1))
        signal_name = match.group(2)

        # 将公式与特定信号关联
        self.signal_formulas[(message_id, signal_name)] = {
            "formula_name": formula_info["name"],
            "params"      : formula_info["params"]
        }

        return (message_id, signal_name)

    def get_physical_value(self, message_id: int, signal_name: str, raw_value: Union[int, float]) -> Union[int, float]:
        """
        根据信号的物理值计算公式计算物理值

        Args:
            message_id: 报文ID
            signal_name: 信号名称
            raw_value: 原始值

        Returns:
            物理值
        """
        # 如果有自定义公式，则使用自定义公式计算
        if (message_id, signal_name) in self.signal_formulas:
            formula_info = self.signal_formulas[(message_id, signal_name)]
            params = {param: raw_value for param in formula_info["params"]}
            # 这里需要实现具体的公式解析和计算逻辑
            # 可以使用eval函数或更安全的表达式解析器
            # 示例中我们假设公式是简单的线性变换
            if len(params) == 1:
                # 如果只有一个参数，使用默认的线性公式
                return raw_value * 0.1 + 5  # 示例公式
            else:
                # 多参数情况处理
                return raw_value  # 默认返回原始值
        # 否则使用标准的因子和偏移量计算
        elif message_id in self.messages and signal_name in self.messages[message_id]["signals"]:
            signal_info = self.messages[message_id]["signals"][signal_name]
            return raw_value * signal_info["factor"] + signal_info["offset"]
        else:
            raise ValueError(f"未找到报文ID {message_id} 和信号 {signal_name} 的定义")

    def get_message_info(self, message_id: int) -> dict:
        """获取报文信息"""
        return self.messages.get(message_id, None)

    def get_all_messages(self) -> dict:
        """获取所有报文信息"""
        return self.messages

    def parse_data(self, message_id: int, recv_data: bytes) -> dict:
        """
        解析原始CAN数据帧，返回各信号的物理值

        Args:
            message_id: 报文ID
            recv_data: 原始CAN数据 (bytes, 长度为1~8)

        Returns:
            dict: {信号名: 物理值}
        """
        if message_id not in self.messages:
            raise ValueError(f"未找到报文ID {hex(message_id)} 的定义")

        message = self.messages[message_id]
        signals = message["signals"]
        result = {}

        int_allbits = []
        big_allbits = ''
        for i in recv_data:
            bits = f"{i:08b}"
            big_allbits += bits
            for y in bits[::-1]:
                int_allbits.append(y)

        for signal_name, signal_info in signals.items():
            start_bit = signal_info["start_bit"]
            bit_length = signal_info["bit_length"]
            isbig_order = signal_info["byte_order"]
            data_type = signal_info["data_type"]

            # 1. 计算信号覆盖的字节范围
            def check_bit(min_pos, max_pos):
                if min_pos < 0:
                    return False, print(f'signal_name= {signal_name}:start bit error pos ?= {min_pos}')

                if max_pos > len(big_allbits):
                    return False, print(f'signal_name= {signal_name}:end bit error pos ?= {max_pos}')

                return True, None

            cal_value = '0b'
            if isbig_order:
                # big_msb 不是顺序bit
                find_pos = (start_bit // 8) * 8 + (7 - start_bit % 8)
                if True in check_bit(find_pos, find_pos + bit_length):
                    for y in range(find_pos, find_pos + bit_length):
                        cal_value += big_allbits[y]

                # big_lsb 不是顺序bit
                # if True in check_bit(find_pos-bit_length, find_pos+1):
                #     for y in range(find_pos-bit_length, find_pos+1):
                #         cal_value += big_allbits[y] """" ""

            else:
                end_bit = start_bit + bit_length - 1
                if True in check_bit(start_bit, end_bit):
                    for y in range(end_bit, start_bit-1, -1):  # 顺序bit 反向解析
                        cal_value += int_allbits[y]

            cal_value = eval(cal_value)

            # 6. 处理有符号数（补码）
            if data_type == "signed" and bit_length > 0:
                sign_mask = 1 << (bit_length - 1)
                if cal_value & sign_mask:
                    cal_value -= (1 << bit_length)

            # 7. 应用公式计算物理值
            end_val = self.get_physical_value(message_id, signal_name, cal_value)
            if end_val > signal_info["max"]:
                end_val = signal_info["max"]
            elif end_val < signal_info["min"]:
                end_val = signal_info["min"]

            if signal_info.get('val_map', False):
                end_val = signal_info['val_map'].get(end_val, end_val)

            signal_name = signal_info.get('lname', signal_name)
            result[signal_name] = (end_val, str(signal_info['unit']).replace('N/A', ''))

        return result


# 使用示例
if __name__ == "__main__":
    data = bytearray([33] * 8)
    msg_id = 670

    parser = DBCParser()
    parser.parse("output_dbc.dbc")  # 替换为实际的DBC文件路径

    parsed_values = parser.parse_data(msg_id, data)
    for signal, value in parsed_values.items():
        print(f"解码 {signal}: {value}")


    # import cantools
    # from pprint import pprint
    #
    # # 加载 DBC 文件
    # db = cantools.database.load_file('tets.dbc')
    #
    # # 查找某个消息（例如 ID=100）
    # msg = db.get_message_by_frame_id(msg_id)
    #
    # # 将数据解码示例（假设原始数据是 motor speed = 1234 * 0.01 = 12.34 m/s）
    # decoded = msg.decode(data)
    # print("\n📦 解码后的数据：")
    # pprint(decoded)
