import json
import re
import argparse

def sanitize_name(name):
    """
    Cleans up a name for use in a DBC file by replacing invalid characters
    with underscores.
    """
    # Replace spaces and other problematic characters with an underscore
    return re.sub(r'[^a-zA-Z0-9_]', '_', name)

def parse_conversion_string(conv_str):
    """
    Parses the 'conversion' string into a dictionary of value-description pairs.
    Example: "0x0:Valid(有效) 0x1:Invalid(无效)"
    """
    if not conv_str or conv_str == "N/A":
        return None
    
    # Use regex to find all "value:description" pairs
    # \S+?: matches the value part (non-greedy)
    # : matches the literal colon
    # \S+: matches the description part
    pairs = re.findall(r'(\S+?):(\S+)', conv_str)
    
    val_table = {}
    for value_str, desc in pairs:
        try:
            # Convert hex or decimal string to integer
            value = int(value_str, 16) if value_str.lower().startswith('0x') else int(value_str)
            val_table[value] = desc
        except ValueError:
            print(f"Warning: Could not parse value '{value_str}' in conversion string.")
            continue
            
    return val_table

def convert_json_to_dbc(json_data):
    """
    Main function to convert JSON data structure to a DBC file string.
    """
    dbc_lines = []
    
    # --- 1. DBC Header ---
    dbc_lines.append('VERSION ""')
    dbc_lines.append("")
    dbc_lines.append("NS_ :")
    dbc_lines.append("	CM_")
    dbc_lines.append("	BA_")
    dbc_lines.append("	VAL_")
    dbc_lines.append("")
    dbc_lines.append("BS_:")
    dbc_lines.append("")

    # --- 2. Nodes (BU_) ---
    # Find all unique senders, add a generic receiver
    nodes = set()
    for msg_info in json_data['communication_matrix']['messages']:
        if 'bms' in msg_info and msg_info['bms'] == 'Tx':
            nodes.add("BMS") # Assuming the sender is always 'BMS'
    
    # Add a default receiver if needed, common practice
    # nodes.add("Vector__XXX")
    nodes.add("ECU")
    
    if not nodes:
        nodes.add("Default_Node") # Fallback
        
    dbc_lines.append(f"BU_: {' '.join(sorted(list(nodes)))}")
    dbc_lines.append("")

    # --- 4. Attribute Definitions (BA_DEF_) ---
    # Define a custom attribute for the initial/start value of a signal.
    # This attribute will be applied to signals (SG_) and is of type HEX.
    # dbc_lines.append('BA_DEF_ SG_ "GenSigStartValue" HEX 0 0;')
    # dbc_lines.append("")

    messages = json_data['communication_matrix']['messages']
    
    # --- 3. Messages (BO_) and Signals (SG_) ---
    for msg_info in messages:
        try:
            msg_name = sanitize_name(msg_info['message'])
            # Convert hex CAN ID string to decimal integer
            msg_id = int(msg_info['can_id'], 16)
            msg_dlc = msg_info['dlc']
            
            # Determine sender node
            sender_node = "BMS" if msg_info.get('bms') == 'Tx' else "ECU"

            dbc_lines.append(f"BO_ {msg_id} {msg_name}: {msg_dlc} {sender_node}")
            
            if 'signals' in msg_info:
                for sig_info in msg_info['signals']:
                    sig_name = sanitize_name(sig_info.get('short_name', sig_info['signal']))
                    
                    # Calculate start bit assuming Motorola (Big Endian) format
                    # DBC start bit is the MSB's bit index.
                    start_bit_in_byte = sig_info['start_bit']
                    start_byte = sig_info['start_byte']
                    start_bit = start_byte * 8 + start_bit_in_byte
                    
                    length = sig_info['len']
                    
                    # Byte order: 0 for Motorola (Big Endian), 1 for Intel (Little Endian)
                    # We assume Big Endian as it's common in automotive.
                    byte_order = 0 # @0
                    
                    # Data type: '+' for unsigned, '-' for signed
                    data_type = sig_info.get('data_type', 'SNT')
                    is_signed = '+' if 'U' in data_type.upper() or 'BLN' in data_type.upper() else '-'
                    
                    factor = sig_info.get('factor', 1)
                    offset = sig_info.get('offset', 0)
                    
                    min_val = sig_info.get('min_phys', 0)
                    # Handle typo in provided JSON structure ('max_phyx')
                    max_val = sig_info.get('max_phyx', sig_info.get('max_phys', 1))

                    unit = sig_info.get('unit', '')
                    
                    # Assume receiver is all other nodes or a generic one
                    # receiver = "Vector__XXX"
                    receiver = "ECU" if sender_node == "BMS" else "BMS"

                    sg_line = (
                        f' SG_ {sig_name} : {start_bit}|{length}@{byte_order}{is_signed}'
                        f' ({factor},{offset})'
                        f' [{min_val}|{max_val}]'
                        f' "{unit}" {receiver}'
                    )
                    dbc_lines.append(sg_line)
            
            dbc_lines.append("") # Newline after each message block
            
        except (KeyError, ValueError) as e:
            print(f"Skipping message due to error: {e}. Message data: {msg_info.get('message', 'N/A')}")
            continue

    # --- 4. Comments (CM_) and Attribute Values (BA_) ---
    # This section adds comments for signal long names and sets the initial values.
    dbc_lines.append("") # Add a separator line
    for msg_info in messages:
        if 'signals' not in msg_info:
            continue
        
        try:
            msg_id = int(msg_info['can_id'], 16)
            for sig_info in msg_info['signals']:
                sig_name = sanitize_name(sig_info.get('short_name', sig_info['signal']))
                
                # Add signal long name as a comment (CM_)
                # The 'signal' field from JSON is used as the long name.
                long_name = sig_info.get('signal', '').replace('"', '""') # Escape quotes in comment
                if long_name:
                    cm_line = f'CM_ SG_ {msg_id} {sig_name} "{long_name}";'
                    dbc_lines.append(cm_line)
                    
                # # Add initial value as a signal attribute (BA_)
                # # The 'initial_value_hex' field is used for the "GenSigStartValue" attribute.
                # initial_value_str = sig_info.get('initial_value_hex')
                # if initial_value_str:
                #     try:
                #         # The DBC attribute value should be the decimal representation of the hex value.
                #         val_as_int = int(initial_value_str, 16)
                #         ba_line = f'BA_ "GenSigStartValue" SG_ {msg_id} {sig_name} {val_as_int};'
                #         dbc_lines.append(ba_line)
                #     except (ValueError, TypeError):
                #         print(f"Warning: Could not parse initial_value_hex '{initial_value_str}' for signal {sig_name}.")

        except (KeyError, ValueError):
            # Errors for this message were likely already caught, but we continue just in case.
            continue

    # --- 5. Value Tables (VAL_) ---
    dbc_lines.append("") # Add a separator line
    for msg_info in messages:
        if 'signals' not in msg_info:
            continue
            
        try:
            msg_id = int(msg_info['can_id'], 16)
            for sig_info in msg_info['signals']:
                if 'conversion' in sig_info and sig_info['conversion']:
                    val_table = parse_conversion_string(sig_info['conversion'])
                    if val_table:
                        sig_name = sanitize_name(sig_info.get('short_name', sig_info['signal']))
                        val_line = f"VAL_ {msg_id} {sig_name} "
                        val_pairs = [f'{val} "{desc.replace("_", " ")}"' for val, desc in val_table.items()]
                        val_line += ' '.join(val_pairs) + " ;"
                        dbc_lines.append(val_line)
        except (KeyError, ValueError) as e:
            # Already handled in the main loop, but good practice to have it here too
            continue
            
    return "\n".join(dbc_lines)

# --- Main execution block ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Convert a JSON CAN definition file to a DBC file.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    # parser.add_argument("input_json", help="Path to the input JSON file.")
    # parser.add_argument("output_dbc", help="Path for the output DBC file.")

    args = parser.parse_args()
    args.input_json = "df.json"
    args.output_dbc = "output_dbc.dbc"

    try:
        print(f"Reading JSON from: {args.input_json}")
        with open(args.input_json, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print("Converting to DBC format...")
        dbc_content = convert_json_to_dbc(data)

        with open(args.output_dbc, 'w', encoding='utf-8') as f:
            f.write(dbc_content)

        print(f"Successfully converted and saved DBC file to: {args.output_dbc}")

    except FileNotFoundError:
        print(f"Error: Input file not found at {args.input_json}")
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {args.input_json}. Please check the file format.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")