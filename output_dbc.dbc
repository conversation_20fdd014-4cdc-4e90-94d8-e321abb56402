VERSION ""

NS_ :
	CM_
	BA_
	VAL_

BS_:

BU_: BMS ECU

BO_ 644 BMS_General_Control_Request_1: 8 BMS
 SG_ BMSChgCurV : 5|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BMSChgCur : 4|13@0+ (0.1,0) [0|500] "A" ECU
 SG_ BMSChgVolV : 21|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BMSChgVol : 20|13@0+ (0.1,0) [0|500] "V" ECU

BO_ 647 BMS_General_Status_1: 8 BMS
 SG_ BatTotCurV : 7|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatTotCur : 6|15@0+ (0.1,-1000) [-1000|1000] "A" ECU
 SG_ BatExtVolV : 22|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatIslResV : 21|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatExtVol : 20|13@0+ (0.1,0) [0|500] "V" ECU
 SG_ BatIslRes : 39|16@0+ (1,0) [0|65534] "kΩ" ECU
 SG_ BMSCC2Sts : 55|2@0- (1,0) [0|1] "N/A" ECU
 SG_ BMSGSt1RC : 51|4@0+ (1,0) [0|15] "N/A" ECU
 SG_ BMSGSt1Ck : 63|8@0+ (1,0) [0|255] "N/A" ECU

BO_ 663 BMS_General_Status_2: 8 BMS
 SG_ BatEgyAvlV : 7|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatSOHV : 6|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatSOCV : 5|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatChgTmsV : 4|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatEgyAvl : 3|12@0+ (0.1,0) [0|100.0] "Kw" ECU
 SG_ BatSOH : 23|8@0+ (1,0) [0|100] "%" ECU
 SG_ BatSOC : 31|8@0+ (1,0) [0|100] "%" ECU
 SG_ BatChgTms : 39|16@0+ (1,0) [0|65535] "N/A" ECU
 SG_ BatRmChgTV : 51|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatRmChgT : 50|11@0+ (1,0) [0|1440] "min" ECU

BO_ 652 BMS_General_Status_3: 8 BMS
 SG_ BatCChPwV : 7|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatCChPw : 6|15@0+ (0.01,0) [0|100] "Kw" ECU
 SG_ BatCDcPwV : 23|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatCDcPw : 22|15@0+ (0.01,0) [0|100] "Kw" ECU

BO_ 651 BMS_General_Status_4: 8 BMS
 SG_ Bat2PChPV : 7|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ Bat2PChP : 6|15@0+ (0.01,0) [0|100] "Kw" ECU
 SG_ Bat2PDcPV : 23|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ Bat2PDcP : 22|15@0+ (0.01,0) [0|100] "Kw" ECU
 SG_ Bat10PChPV : 39|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ Bat10PChP : 38|15@0+ (0.01,0) [0|100] "Kw" ECU
 SG_ Bat10PDcPV : 55|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ Bat10PDcP : 54|15@0+ (0.01,0) [0|100] "Kw" ECU

BO_ 666 BMS_General_Status_5: 8 BMS
 SG_ BatTotVlSV : 5|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatTotVlS : 4|13@0+ (0.1,0) [0|500] "V" ECU
 SG_ BatChgVCpV : 29|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatChgVCp : 28|13@0+ (0.1,0) [0|500] "V" ECU
 SG_ BatIntVolV : 45|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatIntVol : 44|13@0+ (0.1,0) [0|500] "V" ECU

BO_ 667 BMS_General_Status_6: 8 BMS
 SG_ BatMaxCVol : 7|16@0+ (1,0) [0|65534] "mV" ECU
 SG_ BatMaxCVPs : 23|8@0+ (1,0) [0|255] "N/A" ECU
 SG_ BatMinCVol : 31|16@0+ (1,0) [0|65534] "mV" ECU
 SG_ BatMinCVPs : 47|8@0+ (1,0) [0|255] "N/A" ECU
 SG_ BatAvgCVol : 55|16@0+ (1,0) [0|65534] "mV" ECU

BO_ 661 BMS_General_Status_7: 8 BMS
 SG_ BatPowSts : 7|4@0- (1,0) [0|1] "N/A" ECU
 SG_ BatMPosRlS : 3|2@0- (1,0) [0|3] "N/A" ECU
 SG_ BatMNegRlS : 1|2@0- (1,0) [0|3] "N/A" ECU
 SG_ BatPchRlS : 15|2@0- (1,0) [0|3] "N/A" ECU
 SG_ BatChrgSts : 11|3@0- (1,0) [0|2] "N/A" ECU
 SG_ BatFuSts : 8|1@0- (1,0) [0|1] "N/A" ECU
 SG_ BatHvIntS : 23|2@0- (1,0) [0|3] "N/A" ECU
 SG_ BatHeRlS : 21|2@0- (1,0) [0|3] "N/A" ECU
 SG_ BatHeSts : 19|3@0- (1,0) [0|3] "N/A" ECU
 SG_ BMSSts : 31|4@0- (1,0) [0|6] "N/A" ECU
 SG_ Prechrgsts : 27|2@0- (1,0) [0|2] "N/A" ECU

BO_ 649 BMS_General_Status_8: 8 BMS
 SG_ BatAvgTmpV : 7|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatMaxTmpV : 6|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatMinTmpV : 5|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatAvgTemp : 15|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatMaxTemp : 23|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatMaxTPos : 31|8@0+ (1,0) [1|254] "N/A" ECU
 SG_ BatMinTemp : 39|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatMinTPos : 47|8@0+ (1,0) [1|254] "N/A" ECU
 SG_ BatOthFltN : 55|8@0+ (1,0) [0|254] "N/A" ECU
 SG_ BatOthFltL : 63|8@0+ (1,0) [0|254] "N/A" ECU

BO_ 670 BMS_General_Status_9: 8 BMS
 SG_ BatFltLvl : 7|4@0- (1,0) [0|10] "N/A" ECU
 SG_ BatAvgCVSt : 3|2@0- (1,0) [0|3] "N/A" ECU
 SG_ BatTotVSt : 1|2@0- (1,0) [0|2] "N/A" ECU
 SG_ BatTempSts : 15|2@0- (1,0) [0|3] "N/A" ECU
 SG_ BatSOCsts : 13|2@0- (1,0) [0|3] "N/A" ECU
 SG_ BatChgCSt : 9|2@0- (1,0) [0|1] "N/A" ECU
 SG_ BatDchCSt : 23|2@0- (1,0) [0|1] "N/A" ECU
 SG_ BatIslSts : 21|2@0- (1,0) [0|1] "N/A" ECU
 SG_ BatCanErrF : 19|2@0- (1,0) [0|1] "N/A" ECU
 SG_ BatTmpSnF : 17|2@0- (1,0) [0|1] "N/A" ECU
 SG_ BatCurSnF : 31|2@0- (1,0) [0|1] "N/A" ECU
 SG_ BatCVSnF : 29|2@0- (1,0) [0|1] "N/A" ECU
 SG_ BalanceSts : 27|2@0- (1,0) [0|2] "N/A" ECU
 SG_ BatThmRnAl : 25|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ RealSOC : 39|8@0+ (1,0) [0|100] "%" ECU

BO_ 1494 BMS_General_Status_10: 8 BMS
 SG_ RatedEgy : 7|10@0+ (0.1,0) [0|102.2] "Kwh" ECU
 SG_ RatedVol : 13|9@0+ (1,0) [0|510] "V" ECU
 SG_ BatPakTmN : 20|5@0+ (1,0) [1|24] "N/A" ECU
 SG_ TotCelNum : 31|6@0+ (1,12) [1|36] "N/A" ECU
 SG_ TotPakNum : 25|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatPakOrd : 24|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatMxCVPO : 39|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatMnCVPO : 38|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatMxTVPO : 37|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatMnTVPO : 36|1@0+ (1,0) [0|1] "N/A" ECU
 SG_ BatHetPrs : 35|2@0- (1,0) [0|3] "N/A" ECU
 SG_ BatHeatMod : 33|2@0- (1,0) [0|3] "N/A" ECU
 SG_ BMSCode : 47|3@0+ (1,0) [1|7] "N/A" ECU
 SG_ BatInfLen : 44|2@0+ (1,0) [0|2] "N/A" ECU
 SG_ VehConf : 42|3@0+ (1,0) [0|6] "N/A" ECU
 SG_ ModeCode : 55|8@0+ (1,0) [0|254] "N/A" ECU
 SG_ SoftEdt : 63|8@0+ (1,0) [0|254] "N/A" ECU

BO_ 1478 BMS_General_Status_11: 8 BMS
 SG_ BatCelT1 : 7|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT2 : 15|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT3 : 23|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT4 : 31|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT5 : 39|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT6 : 47|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT7 : 55|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT8 : 63|8@0+ (1,-50) [-50|200] "℃" ECU

BO_ 1479 BMS_General_Status_12: 8 BMS
 SG_ BatCelT9 : 7|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT10 : 15|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT11 : 23|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT12 : 31|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT13 : 39|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT14 : 47|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT15 : 55|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT16 : 63|8@0+ (1,-50) [-50|200] "℃" ECU

BO_ 1480 BMS_General_Status_13: 8 BMS
 SG_ BatCelT17 : 7|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT18 : 15|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT19 : 23|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT20 : 31|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT21 : 39|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT22 : 47|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT23 : 55|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ BatCelT24 : 63|8@0+ (1,-50) [-50|200] "℃" ECU

BO_ 1439 BMS_General_Status_14: 8 BMS
 SG_ TmpHtMeb1 : 7|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ TmpHtMeb2 : 15|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ TmpHtMeb3 : 23|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ TmpHtMeb4 : 31|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ TmpHtMeb5 : 39|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ TmpHtMeb6 : 47|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ TmpHtMeb7 : 55|8@0+ (1,-50) [-50|200] "℃" ECU
 SG_ TmpHtMeb8 : 63|8@0+ (1,-50) [-50|200] "℃" ECU

BO_ 1456 BMS_General_Status_15: 8 BMS
 SG_ CelBatN_1 : 7|3@0+ (1.0,0.0) [1|1] "N/A" ECU
 SG_ BatCelV_1 : 4|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_2 : 23|3@0+ (1.0,0.0) [2|2] "N/A" ECU
 SG_ BatCelV_2 : 20|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_3 : 39|3@0+ (1.0,0.0) [3|3] "N/A" ECU
 SG_ BatCelV_3 : 36|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_4 : 55|3@0+ (1.0,0.0) [4|4] "N/A" ECU
 SG_ BatCelV_4 : 52|13@0+ (0.001,0) [0|8.19] "V" ECU

BO_ 1457 BMS_General_Status_16: 8 BMS
 SG_ CelBatN_5 : 7|3@0+ (1.0,0.0) [1|1] "N/A" ECU
 SG_ BatCelV_5 : 4|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_6 : 23|3@0+ (1.0,0.0) [2|2] "N/A" ECU
 SG_ BatCelV_6 : 20|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_7 : 39|3@0+ (1.0,0.0) [3|3] "N/A" ECU
 SG_ BatCelV_7 : 36|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_8 : 55|3@0+ (1.0,0.0) [4|4] "N/A" ECU
 SG_ BatCelV_8 : 52|13@0+ (0.001,0) [0|8.19] "V" ECU

BO_ 1458 BMS_General_Status_17: 8 BMS
 SG_ CelBatN_9 : 7|3@0+ (1.0,0.0) [1|1] "N/A" ECU
 SG_ BatCelV_9 : 4|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_10 : 23|3@0+ (1.0,0.0) [2|2] "N/A" ECU
 SG_ BatCelV_10 : 20|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_11 : 39|3@0+ (1.0,0.0) [3|3] "N/A" ECU
 SG_ BatCelV_11 : 36|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_12 : 55|3@0+ (1.0,0.0) [4|4] "N/A" ECU
 SG_ BatCelV_12 : 52|13@0+ (0.001,0) [0|8.19] "V" ECU

BO_ 1459 BMS_General_Status_18: 8 BMS
 SG_ CelBatN_13 : 7|3@0+ (1.0,0.0) [1|1] "N/A" ECU
 SG_ BatCelV_13 : 4|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_14 : 23|3@0+ (1.0,0.0) [2|2] "N/A" ECU
 SG_ BatCelV_14 : 20|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_15 : 39|3@0+ (1.0,0.0) [3|3] "N/A" ECU
 SG_ BatCelV_15 : 36|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_16 : 55|3@0+ (1.0,0.0) [4|4] "N/A" ECU
 SG_ BatCelV_16 : 52|13@0+ (0.001,0) [0|8.19] "V" ECU

BO_ 1460 BMS_General_Status_19: 8 BMS
 SG_ CelBatN_17 : 7|3@0+ (1.0,0.0) [1|1] "N/A" ECU
 SG_ BatCelV_17 : 4|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_18 : 23|3@0+ (1.0,0.0) [2|2] "N/A" ECU
 SG_ BatCelV_18 : 20|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_19 : 39|3@0+ (1.0,0.0) [3|3] "N/A" ECU
 SG_ BatCelV_19 : 36|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_20 : 55|3@0+ (1.0,0.0) [4|4] "N/A" ECU
 SG_ BatCelV_20 : 52|13@0+ (0.001,0) [0|8.19] "V" ECU

BO_ 1461 BMS_General_Status_20: 8 BMS
 SG_ CelBatN_21 : 7|3@0+ (1.0,0.0) [1|1] "N/A" ECU
 SG_ BatCelV_21 : 4|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_22 : 23|3@0+ (1.0,0.0) [2|2] "N/A" ECU
 SG_ BatCelV_22 : 20|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_23 : 39|3@0+ (1.0,0.0) [3|3] "N/A" ECU
 SG_ BatCelV_23 : 36|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_24 : 55|3@0+ (1.0,0.0) [4|4] "N/A" ECU
 SG_ BatCelV_24 : 52|13@0+ (0.001,0) [0|8.19] "V" ECU

BO_ 1462 BMS_General_Status_21: 8 BMS
 SG_ CelBatN_25 : 7|3@0+ (1.0,0.0) [1|1] "N/A" ECU
 SG_ BatCelV_25 : 4|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_26 : 23|3@0+ (1.0,0.0) [2|2] "N/A" ECU
 SG_ BatCelV_26 : 20|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_27 : 39|3@0+ (1.0,0.0) [3|3] "N/A" ECU
 SG_ BatCelV_27 : 36|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_28 : 55|3@0+ (1.0,0.0) [4|4] "N/A" ECU
 SG_ BatCelV_28 : 52|13@0+ (0.001,0) [0|8.19] "V" ECU

BO_ 1463 BMS_General_Status_22: 8 BMS
 SG_ CelBatN_29 : 7|3@0+ (1.0,0.0) [1|1] "N/A" ECU
 SG_ BatCelV_29 : 4|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_30 : 23|3@0+ (1.0,0.0) [2|2] "N/A" ECU
 SG_ BatCelV_30 : 20|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_31 : 39|3@0+ (1.0,0.0) [3|3] "N/A" ECU
 SG_ BatCelV_31 : 36|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_32 : 55|3@0+ (1.0,0.0) [4|4] "N/A" ECU
 SG_ BatCelV_32 : 52|13@0+ (0.001,0) [0|8.19] "V" ECU

BO_ 1464 BMS_General_Status_23: 8 BMS
 SG_ CelBatN_33 : 7|3@0+ (1.0,0.0) [1|1] "N/A" ECU
 SG_ BatCelV_33 : 4|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_34 : 23|3@0+ (1.0,0.0) [2|2] "N/A" ECU
 SG_ BatCelV_34 : 20|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_35 : 39|3@0+ (1.0,0.0) [3|3] "N/A" ECU
 SG_ BatCelV_35 : 36|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_36 : 55|3@0+ (1.0,0.0) [4|4] "N/A" ECU
 SG_ BatCelV_36 : 52|13@0+ (0.001,0) [0|8.19] "V" ECU

BO_ 1441 BMS_General_Status_24: 8 BMS
 SG_ CelBatN_37 : 7|3@0+ (1.0,0.0) [1|1] "N/A" ECU
 SG_ BatCelV_37 : 4|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_38 : 23|3@0+ (1.0,0.0) [2|2] "N/A" ECU
 SG_ BatCelV_38 : 20|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_39 : 39|3@0+ (1.0,0.0) [3|3] "N/A" ECU
 SG_ BatCelV_39 : 36|13@0+ (0.001,0) [0|8.19] "V" ECU
 SG_ CelBatN_40 : 55|3@0+ (1.0,0.0) [4|4] "N/A" ECU
 SG_ BatCelV_40 : 52|13@0+ (0.001,0) [0|8.19] "V" ECU

BO_ 1497 BMS_General_Status_51: 8 BMS
 SG_ BatManuCd : 7|24@0+ (1,0) [0|0] "N/A" ECU
 SG_ ProTypCode : 31|8@0+ (1,0) [0|0] "N/A" ECU
 SG_ BatTyp : 39|8@0+ (1,0) [0|0] "N/A" ECU
 SG_ SpecCode : 47|24@0+ (1,0) [0|0] "N/A" ECU

BO_ 1498 BMS_General_Status_52: 8 BMS
 SG_ TracInf : 7|48@0+ (1,0) [0|0] "N/A" ECU

BO_ 1499 BMS_General_Status_53: 8 BMS
 SG_ BatProDat : 7|24@0+ (1,0) [0|0] "N/A" ECU

BO_ 1500 BMS_General_Status_54: 8 BMS
 SG_ SerNum : 7|56@0+ (1,0) [0|0] "N/A" ECU

BO_ 1420 BMS_General_Status_28: 8 BMS
 SG_ ChrgVolVal : 7|16@0+ (0.1,0) [0|1000] "V" ECU
 SG_ ChrgCurVal : 23|16@0+ (0.1,-400) [-400|500] "A" ECU

BO_ 1421 BMS_General_Status_29: 8 BMS
 SG_ ChrgMod : 47|8@0- (1,0) [0|255] "N/A" ECU

BO_ 1185 BMS_General_Status_58: 8 BMS
 SG_ BatMxCChV : 7|16@0+ (0.01,0) [0|24] "V" ECU
 SG_ MaxTmpAvl : 23|8@0+ (1,0) [-50|200] "℃" ECU

BO_ 964 BMS_General_Status_59: 8 BMS
 SG_ ArslCncnt : 7|16@0- (1,0) [0|65535] "μg/m³" ECU
 SG_ LoPwInThV : 23|16@0- (1,0) [0|65535] "μg/m³" ECU
 SG_ FogSensFlt : 39|5@0- (1,0) [0|4] "N/A" ECU
 SG_ FogSensSts : 34|3@0- (1,0) [0|1] "N/A" ECU
 SG_ BMSGSt59RC : 55|4@0- (1,0) [0|15] "N/A" ECU
 SG_ BMSGSt59Ck : 63|8@0- (1,0) [0|255] "N/A" ECU

BO_ 1618 NMF_BMS: 8 BMS
 SG_ InitCntBMS : 0|1@0+ (1,0) [0|1] "N/A" ECU

BO_ 364 MCU_General_Status_1: 8 ECU
 SG_ TMActWkSts : 5|3@0- (1,0) [0|6] "N/A" BMS

BO_ 366 MCU_General_Status_2: 8 ECU
 SG_ InvVolV : 34|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ InvVol : 33|10@0+ (1,0) [0|1021] "V" BMS

BO_ 1202 MCU_General_Status_5: 8 ECU
 SG_ MCUSupCode : 15|24@0- (1,0) [0|16777215] "N/A" BMS

BO_ 1619 NMF_MCU: 8 ECU
 SG_ InitCntMCU : 0|1@0+ (1,0) [0|1] "N/A" BMS

BO_ 1541 NMF_EPS: 8 ECU
 SG_ InitCntEPS : 0|1@0+ (1,0) [0|1] "N/A" BMS

BO_ 225 Airbag_Impact_Data: 8 ECU
 SG_ CollsnSig : 0|1@0- (1,0) [0|1] "N/A" BMS

BO_ 1540 NMF_SDM: 8 ECU
 SG_ InitCntSDM : 0|1@0- (1,0) [0|0] "N/A" BMS

BO_ 288 Vehicle_Odometer: 5 ECU
 SG_ VehOdo : 7|32@0+ (0.015625,0) [0|67108864] "km" BMS
 SG_ VehOdoV : 32|1@0- (0,0) [0|0] "N/A" BMS

BO_ 1556 NMF_IC: 8 ECU
 SG_ InitCntIC : 0|1@0- (0,0) [0|0] "N/A" BMS

BO_ 1341 UCU_Send_Time_Message: 8 ECU
 SG_ Second : 7|6@0+ (1,0) [0|59] "s" BMS
 SG_ Minute : 15|6@0+ (1,0) [0|59] "min" BMS
 SG_ GPSSystAtv : 1|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ Hour : 23|5@0+ (1,0) [0|23] "h" BMS
 SG_ Day : 31|5@0+ (1,0) [1|31] "d" BMS
 SG_ Month : 39|4@0+ (1,0) [1|12] "mon" BMS
 SG_ Year : 47|8@0+ (1,2010) [2010|2265] "years" BMS

BO_ 1061 UCU_Requestment_Message_1: 8 ECU
 SG_ RmtCtrlPkH : 39|2@0- (1,0) [0|0] "N/A" BMS
 SG_ RmtHtModRq : 37|2@0- (1,0) [0|3] "N/A" BMS

BO_ 1670 NMF_UCU: 8 ECU
 SG_ InitCntUCU : 0|1@0- (1,0) [0|0] "N/A" BMS

BO_ 1538 NMF_ABS: 8 ECU
 SG_ InitCntABS : 0|1@0+ (1,0) [0|0] "N/A" BMS

BO_ 1578 NMF_LAM: 8 ECU
 SG_ InitCntLAM : 0|1@0+ (1,0) [0|0] "N/A" BMS

BO_ 1270 OBC_General_Status_1: 8 ECU
 SG_ CDUState : 5|4@0- (1,0) [1|15] "N/A" BMS
 SG_ OBCOtpCurV : 15|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ OBCOtpCur : 12|13@0+ (0.1,0) [0|819.1] "A" BMS
 SG_ OBCOtpVltV : 31|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ OBCOtpVlt : 28|13@0+ (0.1,0) [0|819.1] "V" BMS
 SG_ OBCInpVltV : 42|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ OBCInpVlt : 40|9@0+ (1,0) [0|511] "V" BMS

BO_ 1158 OBC_General_Status_2: 8 ECU
 SG_ OBCInpMxC : 23|8@0+ (1,0) [0|255] "A" BMS
 SG_ BMSChVCRAn : 31|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ CCSts : 27|2@0- (1,0) [0|3] "N/A" BMS
 SG_ CPSts : 25|2@0- (1,0) [0|3] "N/A" BMS

BO_ 1473 OBC_General_Status_4: 8 ECU
 SG_ CDUType : 7|4@0- (1,0) [0|15] "N/A" BMS
 SG_ SupCode : 3|4@0- (1,0) [0|3] "N/A" BMS

BO_ 1620 NMF_OBC: 8 ECU
 SG_ InitCntOBC : 0|1@0+ (1,0) [0|1] "N/A" BMS

BO_ 341 VCU_Vehicle_Command_State: 8 ECU
 SG_ BMSHvPwRq : 7|2@0- (1,0) [0|3] "N/A" BMS
 SG_ VecOptMod : 5|4@0- (1,0) [0|15] "N/A" BMS
 SG_ VCUVCSt1RC : 1|2@0+ (1,0) [0|3] "N/A" BMS
 SG_ VecChgRqst : 15|2@0- (1,0) [0|3] "N/A" BMS
 SG_ VCUVCSt1Ck : 63|8@0+ (1,0) [0|255] "N/A" BMS

BO_ 1221 VCU_General_Status_1: 8 ECU
 SG_ VehDrvMod : 13|2@0- (1,0) [0|3] "N/A" BMS
 SG_ KyPstn : 11|2@0- (1,0) [0|3] "N/A" BMS

BO_ 874 VCU_General_Status_2: 8 ECU
 SG_ VehStarWrn : 5|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ VehRdyDrWn : 4|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ PlPulHBrCh : 3|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ DrvLftWrn : 1|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ NoPuChPlWn : 0|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ VehSpdAvDV : 23|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ VehSpdAvD : 22|15@0+ (0.015625,0) [0|511.9844] "km/h" BMS
 SG_ VehTMActSV : 39|1@0+ (1,0) [0|1] "N/A" BMS
 SG_ VehTMActS : 38|15@0+ (1,-16384) [-16384|16383] "rpm" BMS
 SG_ VCUGSt2RC : 55|2@0+ (1,0) [0|3] "N/A" BMS
 SG_ VCUGSt2Ck : 63|8@0+ (1,0) [0|255] "N/A" BMS

BO_ 1617 NMF_VCU: 8 ECU
 SG_ InitCntVCU : 0|1@0+ (1,0) [0|1] "N/A" BMS

BO_ 1536 NMF_BCM: 8 ECU
 SG_ InitCntBCM : 0|1@0+ (1,0) [0|1] "N/A" BMS

BO_ 1577 NMF_TPMS: 8 ECU
 SG_ InitCntTPS : 0|1@0+ (1,0) [0|1] "N/A" BMS

BO_ 906 FDC_General_Status_1: 8 ECU
 SG_ FDCWkSts : 7|4@0- (1,0) [0|15] "N/A" BMS
 SG_ FDCCC2Sts : 2|1@0+ (1,0) [0|1] "N/A" BMS

BO_ 907 FDC_General_Status_2: 8 ECU
 SG_ FDCOtpCur : 42|11@0+ (0.1,0) [0|204.7] "A" BMS

BO_ 910 FDC_General_Status_6: 8 ECU
 SG_ FDCMinOtpV : 7|13@0+ (0.1,0) [0|819.1] "V" BMS
 SG_ FDCMinOtpC : 23|13@0+ (0.1,-400) [-400|419.1] "A" BMS

BO_ 1628 NMF_FDC: 8 ECU
 SG_ InitCntFDC : 0|1@0+ (1,0) [0|1] "N/A" BMS


CM_ SG_ 644 BMSChgCurV "BMS Charge Current Request Validity";
CM_ SG_ 644 BMSChgCur "BMS Charge Current Request";
CM_ SG_ 644 BMSChgVolV "BMS Charge Voltage Request Validity";
CM_ SG_ 644 BMSChgVol "BMS Charge Voltage Request";
CM_ SG_ 647 BatTotCurV "Battery Total Current Validity";
CM_ SG_ 647 BatTotCur "Battery Total Current";
CM_ SG_ 647 BatExtVolV "Battery External Voltage Validity";
CM_ SG_ 647 BatIslResV "Battery Insulation Resistance Validity";
CM_ SG_ 647 BatExtVol "Battery External Voltage";
CM_ SG_ 647 BatIslRes "Battery Insulation Resistance";
CM_ SG_ 647 BMSCC2Sts "BMS CC2 Status";
CM_ SG_ 647 BMSGSt1RC "BMS General Status 1 Rolling Counter";
CM_ SG_ 647 BMSGSt1Ck "BMS General Status 1 Checksum";
CM_ SG_ 663 BatEgyAvlV "Battery Energy Available Validity";
CM_ SG_ 663 BatSOHV "Battery SOH Validity";
CM_ SG_ 663 BatSOCV "Battery SOC Validity";
CM_ SG_ 663 BatChgTmsV "Battery Charge and Discharge Times Validity";
CM_ SG_ 663 BatEgyAvl "Battery Energy Available";
CM_ SG_ 663 BatSOH "Battery SOH";
CM_ SG_ 663 BatSOC "Battery SOC";
CM_ SG_ 663 BatChgTms "Battery Charge and Discharge Times";
CM_ SG_ 663 BatRmChgTV "Battery Remain Charge Time Validity";
CM_ SG_ 663 BatRmChgT "Battery Remain Charge Time";
CM_ SG_ 652 BatCChPwV "Battery Continue Charge Power Available Validity";
CM_ SG_ 652 BatCChPw "Battery Continue Charge Power Available";
CM_ SG_ 652 BatCDcPwV "Battery Continue Discharge Power Available Validity";
CM_ SG_ 652 BatCDcPw "Battery Continue Discharge Power Available";
CM_ SG_ 651 Bat2PChPV "Battery 2S Pulse Charge Power Available Validity";
CM_ SG_ 651 Bat2PChP "Battery 2S Pulse Charge Power Available";
CM_ SG_ 651 Bat2PDcPV "Battery 2S Pulse Discharge Power Available Validity";
CM_ SG_ 651 Bat2PDcP "Battery 2S Pulse Discharge Power Available";
CM_ SG_ 651 Bat10PChPV "Battery 10S Pulse Charge Power Available Validity";
CM_ SG_ 651 Bat10PChP "Battery 10S Pulse Charge Power Available";
CM_ SG_ 651 Bat10PDcPV "Battery 10S Pulse Discharge Power Available Validity";
CM_ SG_ 651 Bat10PDcP "Battery 10S Pulse Discharge Power Available";
CM_ SG_ 666 BatTotVlSV "Battery Total Voltage Sum Validity";
CM_ SG_ 666 BatTotVlS "Battery Total Voltage Sum";
CM_ SG_ 666 BatChgVCpV "Battery Charge Voltage Completed Validity";
CM_ SG_ 666 BatChgVCp "Battery Charge Voltage Completed";
CM_ SG_ 666 BatIntVolV "Battery Internal Voltage Validity";
CM_ SG_ 666 BatIntVol "Battery Internal Voltage";
CM_ SG_ 667 BatMaxCVol "Battery Maximum Cell Voltage";
CM_ SG_ 667 BatMaxCVPs "Battery Maximum Cell Voltage Position";
CM_ SG_ 667 BatMinCVol "Battery Minimum Cell Voltage";
CM_ SG_ 667 BatMinCVPs "Battery Minimum Cell Voltage Position";
CM_ SG_ 667 BatAvgCVol "Battery Average Cell Voltage";
CM_ SG_ 661 BatPowSts "Battery Power Status";
CM_ SG_ 661 BatMPosRlS "Battery Main Positive Relay Status";
CM_ SG_ 661 BatMNegRlS "Battery Main Negative Relay Status";
CM_ SG_ 661 BatPchRlS "Battery Precharge Relay Status";
CM_ SG_ 661 BatChrgSts "Battery Charge Status";
CM_ SG_ 661 BatFuSts "Battery Fuse Status";
CM_ SG_ 661 BatHvIntS "Battery High Voltage Interlocking Status";
CM_ SG_ 661 BatHeRlS "Battery Heat Relay Status";
CM_ SG_ 661 BatHeSts "Battery Heat Status";
CM_ SG_ 661 BMSSts "BMS status";
CM_ SG_ 661 Prechrgsts "Precharge States";
CM_ SG_ 649 BatAvgTmpV "Battery Average Temperature Validity";
CM_ SG_ 649 BatMaxTmpV "Battery Maximum Temperature Validity";
CM_ SG_ 649 BatMinTmpV "Battery Minimum Temperature Validity";
CM_ SG_ 649 BatAvgTemp "Battery Average Temperature";
CM_ SG_ 649 BatMaxTemp "Battery Maximum Temperature";
CM_ SG_ 649 BatMaxTPos "Battery Maximum Temperature Position";
CM_ SG_ 649 BatMinTemp "Battery Minimum Temperature";
CM_ SG_ 649 BatMinTPos "Battery Minimum Temperature Position";
CM_ SG_ 649 BatOthFltN "Battery Other Fault Number";
CM_ SG_ 649 BatOthFltL "Battery Other Fault List";
CM_ SG_ 670 BatFltLvl "Battery Fault Level";
CM_ SG_ 670 BatAvgCVSt "Battery Average Cell Voltage Status";
CM_ SG_ 670 BatTotVSt "Battery Total Voltage Status";
CM_ SG_ 670 BatTempSts "Battery Temperature Status";
CM_ SG_ 670 BatSOCsts "Battery SOC Status";
CM_ SG_ 670 BatChgCSt "Battery Charge Current Status";
CM_ SG_ 670 BatDchCSt "Battery Discharge Current Status";
CM_ SG_ 670 BatIslSts "Battery Insulated Status";
CM_ SG_ 670 BatCanErrF "Battery Inner CAN Bus Fault";
CM_ SG_ 670 BatTmpSnF "Battery Temperature Sensor Fault";
CM_ SG_ 670 BatCurSnF "Battery Current Sensor Fault";
CM_ SG_ 670 BatCVSnF "Battery Cell Voltage Sensor Fault";
CM_ SG_ 670 BalanceSts "Balance Status";
CM_ SG_ 670 BatThmRnAl "Battery Thermal Runaway Alarm Indication";
CM_ SG_ 670 RealSOC "Real SOC";
CM_ SG_ 1494 RatedEgy "Rated Energy";
CM_ SG_ 1494 RatedVol "Rated Voltage";
CM_ SG_ 1494 BatPakTmN "Battery Package Temperature Sensor Number";
CM_ SG_ 1494 TotCelNum "Total Cell Number";
CM_ SG_ 1494 TotPakNum "Total Package Number";
CM_ SG_ 1494 BatPakOrd "Battery Package Order";
CM_ SG_ 1494 BatMxCVPO "Battery Maximum Cell Voltage Package Order";
CM_ SG_ 1494 BatMnCVPO "Battery Minimum Cell Voltage Package Order";
CM_ SG_ 1494 BatMxTVPO "Battery Maximum Cell Temperature Package Order";
CM_ SG_ 1494 BatMnTVPO "Battery Minimum Cell Temperature Package Order";
CM_ SG_ 1494 BatHetPrs "Battery Heat Preservation";
CM_ SG_ 1494 BatHeatMod "Battery Heat Mode";
CM_ SG_ 1494 BMSCode "BMS Code";
CM_ SG_ 1494 BatInfLen "Battery Information Length";
CM_ SG_ 1494 VehConf "Vehicle Configuration";
CM_ SG_ 1494 ModeCode "Mode Code";
CM_ SG_ 1494 SoftEdt "Software Edition";
CM_ SG_ 1478 BatCelT1 "Battery Cell Temperature1";
CM_ SG_ 1478 BatCelT2 "Battery Cell Temperature2";
CM_ SG_ 1478 BatCelT3 "Battery Cell Temperature3";
CM_ SG_ 1478 BatCelT4 "Battery Cell Temperature4";
CM_ SG_ 1478 BatCelT5 "Battery Cell Temperature5";
CM_ SG_ 1478 BatCelT6 "Battery Cell Temperature6";
CM_ SG_ 1478 BatCelT7 "Battery Cell Temperature7";
CM_ SG_ 1478 BatCelT8 "Battery Cell Temperature8";
CM_ SG_ 1479 BatCelT9 "Battery Cell Temperature9";
CM_ SG_ 1479 BatCelT10 "Battery Cell Temperature10";
CM_ SG_ 1479 BatCelT11 "Battery Cell Temperature11";
CM_ SG_ 1479 BatCelT12 "Battery Cell Temperature12";
CM_ SG_ 1479 BatCelT13 "Battery Cell Temperature13";
CM_ SG_ 1479 BatCelT14 "Battery Cell Temperature14";
CM_ SG_ 1479 BatCelT15 "Battery Cell Temperature15";
CM_ SG_ 1479 BatCelT16 "Battery Cell Temperature16";
CM_ SG_ 1480 BatCelT17 "Battery Cell Temperature17";
CM_ SG_ 1480 BatCelT18 "Battery Cell Temperature18";
CM_ SG_ 1480 BatCelT19 "Battery Cell Temperature19";
CM_ SG_ 1480 BatCelT20 "Battery Cell Temperature20";
CM_ SG_ 1480 BatCelT21 "Battery Cell Temperature21";
CM_ SG_ 1480 BatCelT22 "Battery Cell Temperature22";
CM_ SG_ 1480 BatCelT23 "Battery Cell Temperature23";
CM_ SG_ 1480 BatCelT24 "Battery Cell Temperature24";
CM_ SG_ 1439 TmpHtMeb1 "Temperature of Heat Membrane 1";
CM_ SG_ 1439 TmpHtMeb2 "Temperature of Heat Membrane 2";
CM_ SG_ 1439 TmpHtMeb3 "Temperature of Heat Membrane 3";
CM_ SG_ 1439 TmpHtMeb4 "Temperature of Heat Membrane 4";
CM_ SG_ 1439 TmpHtMeb5 "Temperature of Heat Membrane 5";
CM_ SG_ 1439 TmpHtMeb6 "Temperature of Heat Membrane 6";
CM_ SG_ 1439 TmpHtMeb7 "Temperature of Heat Membrane 7";
CM_ SG_ 1439 TmpHtMeb8 "Temperature of Heat Membrane 8";
CM_ SG_ 1456 CelBatN_1 "Cell Battery Number_1";
CM_ SG_ 1456 BatCelV_1 "Battery Cell Voltage_1";
CM_ SG_ 1456 CelBatN_2 "Cell Battery Number_2";
CM_ SG_ 1456 BatCelV_2 "Battery Cell Voltage_2";
CM_ SG_ 1456 CelBatN_3 "Cell Battery Number_3";
CM_ SG_ 1456 BatCelV_3 "Battery Cell Voltage_3";
CM_ SG_ 1456 CelBatN_4 "Cell Battery Number_4";
CM_ SG_ 1456 BatCelV_4 "Battery Cell Voltage_4";
CM_ SG_ 1457 CelBatN_5 "Cell Battery Number_5";
CM_ SG_ 1457 BatCelV_5 "Battery Cell Voltage_5";
CM_ SG_ 1457 CelBatN_6 "Cell Battery Number_6";
CM_ SG_ 1457 BatCelV_6 "Battery Cell Voltage_6";
CM_ SG_ 1457 CelBatN_7 "Cell Battery Number_7";
CM_ SG_ 1457 BatCelV_7 "Battery Cell Voltage_7";
CM_ SG_ 1457 CelBatN_8 "Cell Battery Number_8";
CM_ SG_ 1457 BatCelV_8 "Battery Cell Voltage_8";
CM_ SG_ 1458 CelBatN_9 "Cell Battery Number_9";
CM_ SG_ 1458 BatCelV_9 "Battery Cell Voltage_9";
CM_ SG_ 1458 CelBatN_10 "Cell Battery Number_10";
CM_ SG_ 1458 BatCelV_10 "Battery Cell Voltage_10";
CM_ SG_ 1458 CelBatN_11 "Cell Battery Number_11";
CM_ SG_ 1458 BatCelV_11 "Battery Cell Voltage_11";
CM_ SG_ 1458 CelBatN_12 "Cell Battery Number_12";
CM_ SG_ 1458 BatCelV_12 "Battery Cell Voltage_12";
CM_ SG_ 1459 CelBatN_13 "Cell Battery Number_13";
CM_ SG_ 1459 BatCelV_13 "Battery Cell Voltage_13";
CM_ SG_ 1459 CelBatN_14 "Cell Battery Number_14";
CM_ SG_ 1459 BatCelV_14 "Battery Cell Voltage_14";
CM_ SG_ 1459 CelBatN_15 "Cell Battery Number_15";
CM_ SG_ 1459 BatCelV_15 "Battery Cell Voltage_15";
CM_ SG_ 1459 CelBatN_16 "Cell Battery Number_16";
CM_ SG_ 1459 BatCelV_16 "Battery Cell Voltage_16";
CM_ SG_ 1460 CelBatN_17 "Cell Battery Number_17";
CM_ SG_ 1460 BatCelV_17 "Battery Cell Voltage_17";
CM_ SG_ 1460 CelBatN_18 "Cell Battery Number_18";
CM_ SG_ 1460 BatCelV_18 "Battery Cell Voltage_18";
CM_ SG_ 1460 CelBatN_19 "Cell Battery Number_19";
CM_ SG_ 1460 BatCelV_19 "Battery Cell Voltage_19";
CM_ SG_ 1460 CelBatN_20 "Cell Battery Number_20";
CM_ SG_ 1460 BatCelV_20 "Battery Cell Voltage_20";
CM_ SG_ 1461 CelBatN_21 "Cell Battery Number_21";
CM_ SG_ 1461 BatCelV_21 "Battery Cell Voltage_21";
CM_ SG_ 1461 CelBatN_22 "Cell Battery Number_22";
CM_ SG_ 1461 BatCelV_22 "Battery Cell Voltage_22";
CM_ SG_ 1461 CelBatN_23 "Cell Battery Number_23";
CM_ SG_ 1461 BatCelV_23 "Battery Cell Voltage_23";
CM_ SG_ 1461 CelBatN_24 "Cell Battery Number_24";
CM_ SG_ 1461 BatCelV_24 "Battery Cell Voltage_24";
CM_ SG_ 1462 CelBatN_25 "Cell Battery Number_25";
CM_ SG_ 1462 BatCelV_25 "Battery Cell Voltage_25";
CM_ SG_ 1462 CelBatN_26 "Cell Battery Number_26";
CM_ SG_ 1462 BatCelV_26 "Battery Cell Voltage_26";
CM_ SG_ 1462 CelBatN_27 "Cell Battery Number_27";
CM_ SG_ 1462 BatCelV_27 "Battery Cell Voltage_27";
CM_ SG_ 1462 CelBatN_28 "Cell Battery Number_28";
CM_ SG_ 1462 BatCelV_28 "Battery Cell Voltage_28";
CM_ SG_ 1463 CelBatN_29 "Cell Battery Number_29";
CM_ SG_ 1463 BatCelV_29 "Battery Cell Voltage_29";
CM_ SG_ 1463 CelBatN_30 "Cell Battery Number_30";
CM_ SG_ 1463 BatCelV_30 "Battery Cell Voltage_30";
CM_ SG_ 1463 CelBatN_31 "Cell Battery Number_31";
CM_ SG_ 1463 BatCelV_31 "Battery Cell Voltage_31";
CM_ SG_ 1463 CelBatN_32 "Cell Battery Number_32";
CM_ SG_ 1463 BatCelV_32 "Battery Cell Voltage_32";
CM_ SG_ 1464 CelBatN_33 "Cell Battery Number_33";
CM_ SG_ 1464 BatCelV_33 "Battery Cell Voltage_33";
CM_ SG_ 1464 CelBatN_34 "Cell Battery Number_34";
CM_ SG_ 1464 BatCelV_34 "Battery Cell Voltage_34";
CM_ SG_ 1464 CelBatN_35 "Cell Battery Number_35";
CM_ SG_ 1464 BatCelV_35 "Battery Cell Voltage_35";
CM_ SG_ 1464 CelBatN_36 "Cell Battery Number_36";
CM_ SG_ 1464 BatCelV_36 "Battery Cell Voltage_36";
CM_ SG_ 1441 CelBatN_37 "Cell Battery Number_37";
CM_ SG_ 1441 BatCelV_37 "Battery Cell Voltage_37";
CM_ SG_ 1441 CelBatN_38 "Cell Battery Number_38";
CM_ SG_ 1441 BatCelV_38 "Battery Cell Voltage_38";
CM_ SG_ 1441 CelBatN_39 "Cell Battery Number_39";
CM_ SG_ 1441 BatCelV_39 "Battery Cell Voltage_39";
CM_ SG_ 1441 CelBatN_40 "Cell Battery Number_40";
CM_ SG_ 1441 BatCelV_40 "Battery Cell Voltage_40";
CM_ SG_ 1497 BatManuCd "Battery Manufacturer Code";
CM_ SG_ 1497 ProTypCode "Product Type Code";
CM_ SG_ 1497 BatTyp "Battery Type";
CM_ SG_ 1497 SpecCode "Specification";
CM_ SG_ 1498 TracInf "Traction Information";
CM_ SG_ 1499 BatProDat "Battery Production Date";
CM_ SG_ 1500 SerNum "Serial Number";
CM_ SG_ 1420 ChrgVolVal "Charge Voltage Value";
CM_ SG_ 1420 ChrgCurVal "Charge Current Value";
CM_ SG_ 1421 ChrgMod "Charge Mode";
CM_ SG_ 1185 BatMxCChV "Battery Maximum Cell Charging Voltage Available";
CM_ SG_ 1185 MaxTmpAvl "Maximum Temperature Available";
CM_ SG_ 964 ArslCncnt "Aerosol Concentration";
CM_ SG_ 964 LoPwInThV "Low Power Initialize Threshold Value";
CM_ SG_ 964 FogSensFlt "Fog Sensor Fault";
CM_ SG_ 964 FogSensSts "Fog Sensor Status";
CM_ SG_ 964 BMSGSt59RC "BMS General Status 59 Rolling Counter";
CM_ SG_ 964 BMSGSt59Ck "BMS General Status 59 Checksum";
CM_ SG_ 1618 InitCntBMS "Initialize/Continue Indication On";
CM_ SG_ 364 TMActWkSts "Motor Actual Working Status";
CM_ SG_ 366 InvVolV "Inverter Voltage Validity";
CM_ SG_ 366 InvVol "Inverter Voltage";
CM_ SG_ 1202 MCUSupCode "MCU Supplier Code";
CM_ SG_ 1619 InitCntMCU "Initialize/Continue Indication On";
CM_ SG_ 1541 InitCntEPS "Initialize/Continue Indication On";
CM_ SG_ 225 CollsnSig "Collision signal output";
CM_ SG_ 1540 InitCntSDM "Initialize/Continue Indication On";
CM_ SG_ 288 VehOdo "Vehicle Odometer";
CM_ SG_ 288 VehOdoV "Vehicle Odometer Validity";
CM_ SG_ 1556 InitCntIC "Initialize/Continue Indication On";
CM_ SG_ 1341 Second "Time Second";
CM_ SG_ 1341 Minute "Time Minute";
CM_ SG_ 1341 GPSSystAtv "Global Position System Active";
CM_ SG_ 1341 Hour "Time Hour";
CM_ SG_ 1341 Day "Time Day";
CM_ SG_ 1341 Month "Time Month";
CM_ SG_ 1341 Year "Time Year";
CM_ SG_ 1061 RmtCtrlPkH "Remote Control Parking Heat";
CM_ SG_ 1061 RmtHtModRq "Remote Heat Mode Request";
CM_ SG_ 1670 InitCntUCU "Initialize/Continue Indication On";
CM_ SG_ 1538 InitCntABS "Initialize/Continue Indication On";
CM_ SG_ 1578 InitCntLAM "Initialize/Continue Indication On";
CM_ SG_ 1270 CDUState "CDU State";
CM_ SG_ 1270 OBCOtpCurV "OBC Output Current Validity";
CM_ SG_ 1270 OBCOtpCur "OBC Output Current";
CM_ SG_ 1270 OBCOtpVltV "OBC Output Voltage Validity";
CM_ SG_ 1270 OBCOtpVlt "OBC Output Voltage";
CM_ SG_ 1270 OBCInpVltV "OBC Input Voltage Validity";
CM_ SG_ 1270 OBCInpVlt "OBC Input Voltage";
CM_ SG_ 1158 OBCInpMxC "OBC Input MAX Current";
CM_ SG_ 1158 BMSChVCRAn "BMS Charge Voltage/Current Request Abnormality";
CM_ SG_ 1158 CCSts "CC Status";
CM_ SG_ 1158 CPSts "CP Status";
CM_ SG_ 1473 CDUType "CDU Type";
CM_ SG_ 1473 SupCode "Supplier Code";
CM_ SG_ 1620 InitCntOBC "Initialize/Continue Indication On";
CM_ SG_ 341 BMSHvPwRq "BMS High Voltage Power On Request";
CM_ SG_ 341 VecOptMod "Vehicle Operation Mode";
CM_ SG_ 341 VCUVCSt1RC "VCUVehicleCommandState1 Rolling Counter";
CM_ SG_ 341 VecChgRqst "Vehicle Charging Request";
CM_ SG_ 341 VCUVCSt1Ck "VCU Vehicle Common Status1 Checksum";
CM_ SG_ 1221 VehDrvMod "Vehicle Driving Mode";
CM_ SG_ 1221 KyPstn "Key Position";
CM_ SG_ 874 VehStarWrn "Vehicle Start Warning";
CM_ SG_ 874 VehRdyDrWn "Vehicle Ready to Drive Warning";
CM_ SG_ 874 PlPulHBrCh "Pull Hand Brake When Charging";
CM_ SG_ 874 DrvLftWrn "Drive Left Warning";
CM_ SG_ 874 NoPuChPlWn "Can't Start Pull Out Charge Plug Warning";
CM_ SG_ 874 VehSpdAvDV "Vehicle Speed Average Driven Validity";
CM_ SG_ 874 VehSpdAvD "Vehicle Speed Average Driven";
CM_ SG_ 874 VehTMActSV "Vehicle Motor Actual Speed Validity";
CM_ SG_ 874 VehTMActS "Vehicle Motor Actual Speed";
CM_ SG_ 874 VCUGSt2RC "VCU General Status 2 Rolling Counter";
CM_ SG_ 874 VCUGSt2Ck "VCU General Status 2 Checksum";
CM_ SG_ 1617 InitCntVCU "Initialize/Continue Indication On";
CM_ SG_ 1536 InitCntBCM "Initialize/Continue Indication On";
CM_ SG_ 1577 InitCntTPS "Initialize/Continue Indication On";
CM_ SG_ 906 FDCWkSts "FDC Working Status";
CM_ SG_ 906 FDCCC2Sts "FDC CC2Status";
CM_ SG_ 907 FDCOtpCur "FDC Output Current";
CM_ SG_ 910 FDCMinOtpV "FDC Minimum Output Voltage";
CM_ SG_ 910 FDCMinOtpC "FDC Minimum Output Current";
CM_ SG_ 1628 InitCntFDC "Initialize/Continue Indication On_FDC";

VAL_ 644 BMSChgCurV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 644 BMSChgVolV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 647 BatTotCurV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 647 BatExtVolV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 647 BatIslResV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 647 BMSCC2Sts 0 "NO CC2 Enable" 1 "CC2 Enable" ;
VAL_ 663 BatEgyAvlV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 663 BatSOHV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 663 BatSOCV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 663 BatChgTmsV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 663 BatChgTms 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 663 BatRmChgTV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 652 BatCChPwV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 652 BatCDcPwV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 651 Bat2PChPV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 651 Bat2PDcPV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 651 Bat10PChPV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 651 Bat10PDcPV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 666 BatTotVlSV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 666 BatChgVCpV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 666 BatIntVolV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 661 BatPowSts 0 "Power Off(电源断开)" 1 "Power On(电源连接)" ;
VAL_ 661 BatMPosRlS 0 "Open(断开)" 1 "Closed(连接)" 2 "Adhesion(粘连)" 3 "Break(开路)" ;
VAL_ 661 BatMNegRlS 0 "Open(断开)" 1 "Closed(连接)" 2 "Adhesion(粘连)" 3 "Break(开路)" ;
VAL_ 661 BatPchRlS 0 "Open(断开)" 1 "Closed(连接)" 2 "Adhesion(粘连)" 3 "Break(开路)" ;
VAL_ 661 BatChrgSts 0 "Disable(不可充电)" 1 "slow charge Enable(慢充可充电)" 2 "Finish(充电完成)" 3 "fast charge Enable(快充可充电)" ;
VAL_ 661 BatFuSts 0 "Normal(正常)" 1 "Broken(熔断)" ;
VAL_ 661 BatHvIntS 0 "Open(断开)" 1 "Closed(连接)" 2 "Fault(故障)" ;
VAL_ 661 BatHeRlySts 0 "Open(断开)" 1 "Closed(连接)" 2 "Adhesion(粘连)" 3 "Break(开路)" ;
VAL_ 661 BatHeSts 0 "NO heat(断开)" 1 "Heat(加热)" 2 "Finish(加热完成)" 3 "Fault(加热故障)" ;
VAL_ 661 BMSSts 0 "Initial(初始化)" 1 "Idle(待机)" 2 "Charging(充电)" 3 "Run(行驶)" 4 "Error(故障)" 5 "Sleep(休眠)" 6 "(预留)" ;
VAL_ 661 Prechrgsts 0 "Normal(正常)" 1 "Failure(失败)" ;
VAL_ 649 BatAvgTempV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 649 BatMaxTempV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 649 BatMinTempV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 670 BatFltLvl 0 "Normal(正常)" 1 "DisCharge Tiny Fault(放电轻微故障)" 2 "DisCharge Performance Limited(放电功率限制)" 3 "DisCharge Limp Performance(放电跛行行车)" 4 "DisCharge National Stardard's Three-level Warning(放电国标三级预警)" 5 "battery DisCharge Open Request(电池停车请求)" 6 "Charge Tiny Fault(充电轻微故障)" 7 "Charge Performance Limited(充电功率限制)" 8 "Charge National Standard's Three-lever Warning(充电国标三级预警)" 9 "battery Charge Open Request(充电立即停止)" 10 "Urgent Shutoff(紧急停止)" ;
VAL_ 670 BatAvgCVSt 0 "Normal(正常)" 1 "Over Voltage(电压过高)" 2 "Under Voltage(电压过低)" 3 "Inconfomity(电压不均衡)" ;
VAL_ 670 BatTotVSt 0 "Normal(正常)" 1 "Over Voltage(电压过高)" 2 "Under Voltage(电压过低)" ;
VAL_ 670 BatTempSts 0 "Normal(正常)" 1 "Over Temperature(温度过高)" 2 "Under Temperature(温度过低)" 3 "Inconformity(温度不均衡)" ;
VAL_ 670 BatSOCsts 0 "Normal(正常)" 1 "Over Top(SOC过高)" 2 "Over Bottom(SOC过低)" ;
VAL_ 670 BatChgCSt 0 "Normal(正常)" 1 "Over Current(充电过流)" ;
VAL_ 670 BatDchCSt 0 "Normal(正常)" 1 "Over Current(放电过流)" ;
VAL_ 670 BatIslatdSts 0 "Normal(正常)" 1 "Low Insulated(绝缘值低)" ;
VAL_ 670 BatCanErrF 0 "Normal(正常)" 1 "Abnormal(异常)" ;
VAL_ 670 BatTmpSnF 0 "Normal(正常)" 1 "Abnormal(异常)" ;
VAL_ 670 BatCurSnF 0 "Normal(正常)" 1 "Abnormal(异常)" ;
VAL_ 670 BatCVSnF 0 "Normal(正常)" 1 "Abnormal(异常)" ;
VAL_ 670 BalanceSts 0 "No Balance(无均衡)" 1 "Open(开启均衡)" 2 "False(均衡错误)" ;
VAL_ 670 BatThmRnAl 0 "Normal(正常)" 1 "Alarm(报警)" ;
VAL_ 1494 BatHetPrs 0 "ON(开启保温功能)" 1 "OFF(关闭保温功能)" 2 "Reserved(保留)" ;
VAL_ 1494 BatHeatMod 0 "加热模式1" 1 "加热模式2" 2 "保留" ;
VAL_ 1494 BatInfLen 0 "24" 1 "26" ;
VAL_ 1421 ChrgMod 0 "恒压充电" 1 "恒流充电" ;
VAL_ 964 FogSensFlt 0 "正常" 1 "短路或断路" 2 "供电过压故障" 3 "供电欠压故障" ;
VAL_ 964 FogSensSts 0 "正常" 1 "报警" ;
VAL_ 1618 InitCntBMS 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 364 TMActWkSts 0 "Not Ready(未准备完毕)" 1 "Low Voltage Selftest Pass(低压自检通过)" 2 "High Voltage Selftest Pass(高压自检通过)" 3 "Ready(准备完毕)" 5 "Fault 1(故障1)" 6 "Fault 2(故障2)" ;
VAL_ 366 InvVolV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 1619 InitCntMCU 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 1541 InitCntEPS 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 225 CollsnSig 0 "False(未发生碰撞)" 1 "Ture(发生碰撞)" ;
VAL_ 1540 InitCntSDM 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 288 VehOdoV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 1556 InitCntIC 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 1341 GPSSystAtv 0 "GPS已定位" 1 "GPS未定位" ;
VAL_ 1061 RmtCtrlPkH 0 "无请求" 1 "驻车保温功能打开" 2 "驻车保温功能关闭" ;
VAL_ 1061 RmtHtModRq 0 "无请求" 1 "加热模式1" 2 "加热模式2" ;
VAL_ 1670 InitCntUCU 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 1538 InitCntABS 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 1578 InitCntLAM 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 1270 CDUState 0 "Reserve(预留)" 1 "Charge prepare(充电准备完成)" 2 "Charge Normal Working(充电正常工作)" 3 "Charge Limited Working(充电限功率工作)" 4 "Error(OBC或DC/AC故障)" 5 "Idle(OBC或DC/AC待机)" 6 "Charge Connected Prepare(充电连接准备)" 7 "Reserve(保留)" 8 "Charge Connected confirmd(充电连接已确认)" 9 "Charge Control confirmed(充电控制已确认)" 10 "Charge Input confirmed(充电输入已确认)" 12 "Discharge prepare(放电准备完成)" 13 "Discharge Normal Working(放电正常工作)" 14 "Discharge Limited Working(放电限功率工作)" ;
VAL_ 1270 OBCOtpCurV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 1270 OBCOtpVltV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 1270 OBCInpVltV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 1158 BMSChVCRAn 0 "False(正常状态)" 1 "True(故障)" ;
VAL_ 1158 CCSts 0 "CC未连接" 1 "CC半连接" 2 "CC充电连接" 3 "CC放电连接" ;
VAL_ 1158 CPSts 0 "CP未连" 1 "CP异常" 2 "CP连接" ;
VAL_ 1473 CDUType 0 "2kwOBC" 1 "3.3kwOBC" 2 "6.6kwOBC" 3 "2kwOBC+1.5kwDC/DC" 4 "6.6kwOBC+1.5kwDC/DC" 5 "6.6kwOBC+1.5kwDC/DC+2kwDC/AC" 6 "6.6kwOBC+1.5kwDC/DC+PDU" 7 "6.6kwOBC+2kwDC/DC+PDU" 8 "6.6kwOBC+1.5kwDC/DC+2kwDC/AC+PDU" 9 "1.5kwOBC+0.8kwDC/DC+PDU" 10 "2kwOBC+1kwDC/DC+PDU" ;
VAL_ 1473 SupCode 0 "杭州铁城" 1 "重庆力华" 2 "珠海英搏尔" 3 "保留" ;
VAL_ 1620 InitCntOBC 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 341 BMSHvPwRq 0 "Open HV Switch(断开高压开关)" 1 "Open HV switch urgently(紧急断开高压开关)" 2 "Close HV switch(闭合高压开关)" ;
VAL_ 341 VecOptMod 0 "Sleep(休眠)" 1 "Initial(初始化)" 2 "Power Up(上电)" 3 "Run(行驶)" 4 "Charging(充电)" 5 "Power Down(下电)" 6 "Error(故障)" 7 "Limp Home" 8 "LV Charging(馈电补电)" ;
VAL_ 341 VecChrgRqst 0 "Forbid Charging(禁止充电)" 1 "Permit Charging(允许充电)" 2 "Finish Charging(充电完成)" ;
VAL_ 1221 VehDrvMod 0 "Normal(正常模式)" 1 "Eco(经济模式)" 2 "Sport(运动模式)" 3 "Reserved(预留)" ;
VAL_ 1221 KyPstn 0 "OFF" 1 "ACC" 2 "ON" ;
VAL_ 874 VehStarWrn 0 "No Warning" 1 "Warning(请踩住刹车踏板)" ;
VAL_ 874 VehRdyDrWn 0 "No Warning" 1 "Warning(请挂档行驶)" ;
VAL_ 874 PlPulHBrCh 0 "No Warning" 1 "Warning(充电时请拉起手刹)" ;
VAL_ 874 DrvLftWrn 0 "No Warning" 1 "Warning(下车时请拔掉钥匙)" ;
VAL_ 874 NoPuChPlWn 0 "No Warning" 1 "Warning(行驶前请拔掉充电枪)" ;
VAL_ 874 VehSpdAvDV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 874 VehTMActSV 0 "Valid(有效)" 1 "Invalid(无效)" ;
VAL_ 1617 InitCntVCU 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 1536 InitCntBCM 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 1577 InitCntTPS 0 "Continue(保持)" 1 "Initialize(唤醒)" ;
VAL_ 906 FDCWkSts 0 "初始化" 1 "BOOST" 2 "正常工作" 3 "限功率工作" ;
VAL_ 906 FDCCC2Sts 0 "False" 1 "Connected" ;
VAL_ 1628 InitCntFDC 0 "Continue(保持)" 1 "Initialize(唤醒)" ;